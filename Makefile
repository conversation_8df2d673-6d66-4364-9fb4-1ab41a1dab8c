include $(PWD)/build.mk

export CROSS	:= aarch64-mix210-linux-
export CC		:= $(CROSS)gcc
export CXX		:= $(CROSS)g++
export AR		:= $(CROSS)ar
export STRIP	:= $(CROSS)strip


CFLAGS += -DOT_ARCH=ss928v100
#CFLAGS +=-DSENSOR0_TYPE=$(SENSOR_TYPE)
CFLAGS += -DOT_MIPI_ENABLE

LIBS_CFLAGS    += -mcpu=cortex-a53
LIBS_LD_CFLAGS += -mcpu=cortex-a53

# common CFLAGS
LIBS_CFLAGS    += -fno-aggressive-loop-optimizations -ldl -ffunction-sections -fdata-sections -O2
LIBS_LD_CFLAGS += -fno-aggressive-loop-optimizations

# secure CFLAGS
LIBS_CFLAGS      += -fstack-protector-strong -fPIC
#LIBS_EXEC_CFLAGS += -fPIE -pie -s
LIBS_LD_CFLAGS   += -Wl,-z,relro -Wl,-z,noexecstack -Wl,-z,now,-s

CFLAGS +=$(LIBS_CFLAGS)
CFLAGS +=$(LIBS_LD_CFLAGS)



CFLAGS += -I$(PWD)/src
CFLAGS += -Wall -Wshadow -Wundef -Wmaybe-uninitialized -O3 -g0
SRCS		:= $(wildcard src/*.c)

SRCS := $(wildcard ./src/*.c)
SRCS += $(wildcard ./src/ipc/*.c)
SRCS += $(wildcard ./src/ipc/infrared/*.c)
SRCS += $(wildcard ./src/ini/*.c)
SRCS += $(wildcard ./src/common/*.c)
CFLAGS += -I$(PWD)/src/ipc -I$(PWD)/src/ipc/infrared -I$(PWD)/src/ipc/nanomsg/include  -I$(PWD)/src/ini -I$(PWD)/src/common
CFLAGS += -I./src/include

##############
# Get the Git commit ID
##Version Information Compilation Options
CFLAGS+=-DGIT_SHA1="$(shell git log --format='[sha1]:%h [author]:%cn [time]:%ci [commit]:%s [branch]:%d' -1)"
CFLAGS+=-DGIT_DESC="$(shell git describe --tags --always --dirty)"


CFLAGS += -DENABLE_DAHUA_SDK
#CFLAGS += -DENABLE_INIT_PIPELINE
CFLAGS += -DENABLE_REPORT_TEMPERATURE

OBJS		:= $(SRCS:%.c=%.o)


# ����Ŀ���ļ��б�

TARGET	:= infra_uvc


.PHONY	: clean all

all: $(TARGET)

$(TARGET): $(OBJS) 
	$(CXX) $(CFLAGS) $(LIB_PATH) -o $@ $^ $(LIBS) -Wl,-gc-sections
	$(STRIP) $(TARGET)

strip:
	$(STRIP) $(TARGET)


clean:
	@rm -f src/*.o
	@rm -f $(TARGET)
	@rm -f $(OBJS)
	@rm -f $(OBJS_C)
	@rm -f $(OBJS_COMM_C)
	@rm -f src/*.tmp
	
check_dir:
	@if [ -z "$(TARGET_DIR)" ]; then \
		echo "Error: TARGET_DIR variable not set"; \
		exit 1; \
	fi
	@if [ -d "$(TARGET_DIR)" ] && [ -x "$(TARGET_DIR)" ]; then \
		echo "Directory $(TARGET_DIR) exists and is accessible"; \
	else \
		echo "Error: Directory $(TARGET_DIR) does not exist or is not accessible"; \
		exit 1; \
	fi


install:check_dir 
ifneq ("$(wildcard $(TARGET_DIR))","")
	 @echo "Installing to $(DIR)..."
	 cp $(TARGET) ${TARGET_DIR} && echo "sync to NFS Done!"
endif

