include $(PWD)/build.mk

export CROSS	:= aarch64-mix210-linux-
export CC		:= $(CROSS)gcc
export CXX		:= $(CROSS)g++
export AR		:= $(CROSS)ar
export STRIP	:= $(CROSS)strip


CFLAGS += -DOT_ARCH=ss928v100
#CFLAGS +=-DSENSOR0_TYPE=$(SENSOR_TYPE)
CFLAGS += -DOT_MIPI_ENABLE

LIBS_CFLAGS    += -mcpu=cortex-a53
LIBS_LD_CFLAGS += -mcpu=cortex-a53

# common CFLAGS
LIBS_CFLAGS    += -fno-aggressive-loop-optimizations -ldl -ffunction-sections -fdata-sections -O2
LIBS_LD_CFLAGS += -fno-aggressive-loop-optimizations

# secure CFLAGS
LIBS_CFLAGS      += -fstack-protector-strong -fPIC
#LIBS_EXEC_CFLAGS += -fPIE -pie -s
LIBS_LD_CFLAGS   += -Wl,-z,relro -Wl,-z,noexecstack -Wl,-z,now,-s

CFLAGS +=$(LIBS_CFLAGS)
CFLAGS +=$(LIBS_LD_CFLAGS)



# Common flags for both C and C++
COMMON_FLAGS += -I$(PWD)/src
COMMON_FLAGS += -Wall -Wshadow -Wundef -Wmaybe-uninitialized -O3 -g0
COMMON_FLAGS += -I$(PWD)/src/ipc -I$(PWD)/src/ipc/infrared -I$(PWD)/src/ipc/nanomsg/include  -I$(PWD)/src/ini -I$(PWD)/src/common
COMMON_FLAGS += -I./src/include
# Define linux macro to ensure DLLEXPORT is properly defined
COMMON_FLAGS += -Dlinux

# C specific flags
CFLAGS += $(COMMON_FLAGS)

# C++ specific flags
CXXFLAGS += $(COMMON_FLAGS)
CXXFLAGS += -std=c++11

# Collect C source files
C_SRCS := $(wildcard ./src/*.c)
C_SRCS += $(wildcard ./src/ipc/*.c)
C_SRCS += $(wildcard ./src/ipc/infrared/*.c)
C_SRCS += $(wildcard ./src/ini/*.c)
C_SRCS += $(wildcard ./src/common/*.c)

# Collect C++ source files
CXX_SRCS := $(wildcard ./src/*.cpp)
CXX_SRCS += $(wildcard ./src/ipc/*.cpp)
CXX_SRCS += $(wildcard ./src/ipc/infrared/*.cpp)
CXX_SRCS += $(wildcard ./src/ini/*.cpp)
CXX_SRCS += $(wildcard ./src/common/*.cpp)

##############
# Get the Git commit ID
##Version Information Compilation Options
# Git version information and feature flags (apply to both C and C++)
VERSION_FLAGS := -DGIT_SHA1="$(shell git log --format='[sha1]:%h [author]:%cn [time]:%ci [commit]:%s [branch]:%d' -1)"
VERSION_FLAGS += -DGIT_DESC="$(shell git describe --tags --always --dirty)"
VERSION_FLAGS += -DENABLE_DAHUA_SDK
#VERSION_FLAGS += -DENABLE_INIT_PIPELINE
VERSION_FLAGS += -DENABLE_REPORT_TEMPERATURE

CFLAGS += $(VERSION_FLAGS)
CXXFLAGS += $(VERSION_FLAGS)

# Generate object files for C and C++ sources
C_OBJS := $(C_SRCS:%.c=%.o)
CXX_OBJS := $(CXX_SRCS:%.cpp=%.o)
OBJS := $(C_OBJS) $(CXX_OBJS)


# ����Ŀ���ļ��б�

TARGET	:= infra_uvc


.PHONY	: clean all

all: $(TARGET)

# Compilation rules for C files
%.o: %.c
	$(CC) $(CFLAGS) -c $< -o $@

# Compilation rules for C++ files
%.o: %.cpp
	$(CXX) $(CXXFLAGS) -c $< -o $@

$(TARGET): $(OBJS)
	$(CXX) $(CXXFLAGS) $(LIB_PATH) -o $@ $^ $(LIBS) -Wl,-gc-sections
	$(STRIP) $(TARGET)

strip:
	$(STRIP) $(TARGET)


clean:
	@rm -f src/*.o
	@rm -f $(TARGET)
	@rm -f $(C_OBJS)
	@rm -f $(CXX_OBJS)
	@rm -f src/*.tmp
	
check_dir:
	@if [ -z "$(TARGET_DIR)" ]; then \
		echo "Error: TARGET_DIR variable not set"; \
		exit 1; \
	fi
	@if [ -d "$(TARGET_DIR)" ] && [ -x "$(TARGET_DIR)" ]; then \
		echo "Directory $(TARGET_DIR) exists and is accessible"; \
	else \
		echo "Error: Directory $(TARGET_DIR) does not exist or is not accessible"; \
		exit 1; \
	fi


install:check_dir 
ifneq ("$(wildcard $(TARGET_DIR))","")
	 @echo "Installing to $(DIR)..."
	 cp $(TARGET) ${TARGET_DIR} && echo "sync to NFS Done!"
endif

