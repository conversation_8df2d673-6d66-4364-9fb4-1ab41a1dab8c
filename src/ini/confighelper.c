
#include "dictionary.h"
#include "iniparser.h"
#include <sys/wait.h>


int execute_command(const char *command) {
    int ret = system(command);
    if (ret == -1) {
        perror("system");
        return -1;
    }
    int exit_status = WEXITSTATUS(ret);
    
    if (exit_status == 0) {
        printf("Command executed successfully.\n");
        return 0;
    } else {
        printf("Command failed with exit status: %d\n", exit_status);
        return exit_status;
    }
}

int sync_config()
{
    char cmd[256] = {0};
    sprintf(cmd, "ql_save_config.sh\n");
    return execute_command(cmd);

}

int load_cfg_from_ini_demo(const char *cfg_file_path)
{
    dictionary  *   ini ;

    /* Some temporary variables to hold query results */
    int             b ;
    int             i ;
    double          d ;
    const char  *   s ;

    ini = iniparser_load(cfg_file_path);
    if (ini==NULL) {
        fprintf(stderr, "cannot parse file: %s\n", cfg_file_path);
        return -1 ;
    }
    //iniparser_dump(ini, stderr);

    /* Get pizza attributes */
    printf("Pizza:\n");

    b = iniparser_getint(ini, "bytetrack:show_lost_len", -1);
    printf("show_lost_len:       [%d]\n", b);
    b = iniparser_getboolean(ini, "nanotrack:nanotrackenable", -1);
    printf("nanotrackenable: [%d]\n", b);
    b = iniparser_getboolean(ini, "configinfo:retain_custom_cfg", -1);
    printf("retain_custom_cfg:    [%d]\n", b);
    b = iniparser_getboolean(ini, "pizza:cheqese", -1);
    printf("Cheese:    [%d]\n", b);

    /* Get wine attributes */
    printf("Wine:\n");
    s = iniparser_getstring(ini, "wine:grape", NULL);
    printf("Grape:     [%s]\n", s ? s : "UNDEF");

    i = iniparser_getint(ini, "wine:year", -1);
    printf("Year:      [%d]\n", i);

    s = iniparser_getstring(ini, "tracking:tracksampledivisor", NULL);
    printf("tracksampledivisor1:   [%s]\n", s ? s : "UNDEF");

    d = iniparser_getint(ini, "tracking:tracksampledivisor", -1.0);
    printf("tracksampledivisor2:   [%g]\n", d);

    iniparser_freedict(ini);
    return 0 ;
}




int write_to_ini(const char *ini_name, const char *section, const char *section_key, const char * val)
{
    void *dictionary;
    FILE *ini_file;
    int ret = 0;

    if (!ini_name) {
        fprintf(stderr, "Invalid argurment\n");
        return -1;
    }

    dictionary = iniparser_load(ini_name);


    if (!dictionary) {
        fprintf(stderr, "cannot parse file: %s\n", ini_name);
        return -1;
    }
    /* set section */
    ret = iniparser_set(dictionary, section, NULL);

    if (ret < 0) {
        fprintf(stderr, "cannot set section in: %s\n", ini_name);
        ret = -1;
        goto free_dict;
    }

    /* set key/value pair */
    //ret = iniparser_set(dictionary, "Pizza:Cheese", "TRUE");
    ret = iniparser_set(dictionary, section_key, val);

    if (ret < 0) {
        fprintf(stderr, "cannot set key/value in: %s\n", ini_name);
        ret = -1;
        goto free_dict;
    }

    ini_file = fopen(ini_name, "w+");

    if (!ini_file) {
        fprintf(stderr, "iniparser: cannot create example.ini\n");
        ret = -1;
        goto free_dict;
    }

    iniparser_dump_ini(dictionary, ini_file);
    fclose(ini_file);
    sync_config();
free_dict:
    iniparser_freedict(dictionary);
    return ret;
}