#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>

#include "include/libirparse.h"
#include "include/libircmd.h"
#include "include/libiruvc.h"
#include "include/libircam.h"
#include "sample.h"
// #include "iray_pipeline.h"
// #include "uvc.h"


// 全局变量用于存储相机信息
//static vs_uvc_camera_info_s g_camera_info = {0};
static frame_callback_func g_frame_callback = NULL;

// 全局变量用于存储IRAY相关句柄
static IrVideoHandle_t* g_ir_video_handle = NULL;
static IruvcHandle_t *g_iruvc_handle = NULL;
static IrControlHandle_t* g_ir_control_handle = NULL;
static IrcmdHandle_t* g_ircmd_handle = NULL;
static uint8_t* g_raw_frame_data = NULL;
static int g_raw_byte_size = 0;
static int g_is_running = 0;


#if 0
int start_iray_proc(void *callback)
{
    printf("start_iray_proc: Starting IRAY mini2 process\n");

    // 设置优先级
#if defined(_WIN32)
    SetPriorityClass(GetCurrentProcess(), HIGH_PRIORITY_CLASS);
#elif defined(linux) || defined(unix)
    setpriority(PRIO_PROCESS, 0, -20);
#endif

    g_frame_callback = (frame_callback_func)callback;

    // Configuration parameters
    int width = 1280;                                // The width of all data
    int image_height = 1024;                         // The height of image data
    int temp_height = 1024;                          // The height of temperature data
    int info_height = 2;                            // The height of information data(information line)
    int dummy_height = 0;                           // The height of dummy data (used when there are two information lines in the frame information)
    int image_ratio = 2;                            // Image data one pixel point occupies two bytes
    int temp_ratio = 2;                             // Temperature data one pixel point occupies two bytes
    int info_ratio = 2;                             // Information data one pixel point occupies two bytes
    int dummy_ratio = 2;                            // Used when there are two information lines in the frame information
    char output_type[10] = "YUYV";                  // The output format of the image data
    int output_format =  ADV_USB_FORMAT;            // The output format of the video data
    int output_fps = 30;                            // Frame rate
    int output_num = DOUBLE_VIDEO;                  // Used to indicate whether to output a single image or dual images
    int output_status = BASIC_ENABLE;               // The status of the video output, whether to enable the video output
    //MINI2
    // int pid = 0x43E1;                            // The device's PID
    // int vid = 0x3474;                            // The device's VID
    int pid = 0x00c3;                               // The device's PID
    int vid = 0x04b4;                               // The device's VID
    int same_idx = 0;                               // When only one device is being used, set to 0

    // Calculate the size of the data
    int image_byte_size = width * image_height * image_ratio;
    int temp_byte_size = width * temp_height * temp_ratio;
    int info_byte_size = width * info_height * info_ratio;
    int dummy_byte_size = width * dummy_height * dummy_ratio;
    g_raw_byte_size = image_byte_size + temp_byte_size + info_byte_size + dummy_byte_size;

    // All parameters used
    IruvcDevParam_t dev_param;
    memset(&dev_param, 0, sizeof(IruvcDevParam_t));
    VideoOutputInfo_t output_info;
    IruvcCamStreamParams_t video_params;
    memset(&video_params, 0, sizeof(IruvcCamStreamParams_t));
    IruvcStreamStopParams_t stop_params;
    memset(&stop_params, 0, sizeof(IruvcStreamStopParams_t));

    int ret = IRLIB_SUCCESS;

    // Print the version information of the libraries
    printf("Depends lib version:\n");
    printf("ircmd version:%s\n", ircmd_version());
    printf("ircam version:%s\n", ircam_version());
    printf("iruvc version:%s\n", iruvc_version());

    // Register library logs and set log level to ERROR
    ircam_log_register(IRCAM_LOG_ERROR, NULL, NULL);
    ircmd_log_register(IRCMD_LOG_ERROR, NULL, NULL);
    iruvc_log_register(IRUVC_LOG_ERROR, NULL, NULL);

    // create video handle
    ir_video_handle_create(&g_ir_video_handle);
    g_iruvc_handle = iruvc_camera_handle_create(g_ir_video_handle);

    dev_param.pid = pid;
    dev_param.vid = vid;
    dev_param.same_idx = same_idx;
    ret = g_ir_video_handle->ir_video_open(g_iruvc_handle, &dev_param);
    if (ret != IRLIB_SUCCESS)
    {
        printf("fail to open video handle, ret is %d\n", ret);
        goto fail;
    }

    ret = g_ir_video_handle->ir_video_init(g_iruvc_handle, &dev_param);
    if (ret != IRLIB_SUCCESS)
    {
        printf("fail to init video handle, ret is %d\n", ret);
        g_ir_video_handle->ir_video_close(g_iruvc_handle);
        goto fail;
    }
    printf("video handle create success\n");

    // create control handle
    ir_control_handle_create(&g_ir_control_handle);
    ret = iruvc_usb_handle_create_with_exist_instance(g_ir_control_handle, g_iruvc_handle);
    if (ret != IRLIB_SUCCESS)
    {
        printf("fail to create iruvc handle, ret is %d\n", ret);
        g_ir_video_handle->ir_video_release(g_iruvc_handle, &dev_param);
        g_ir_video_handle->ir_video_close(g_iruvc_handle);
        goto fail;
    }
    // Call ircmd_create_handle to create ircmd_handle
    g_ircmd_handle = ircmd_create_handle(g_ir_control_handle);

    // Send the image output command
    output_info.video_output_format = output_format;
    output_info.video_output_fps = output_fps;
    output_info.video_output_num = output_num;
    output_info.video_output_status = output_status;
    ret = adv_digital_video_output_set(g_ircmd_handle, output_info);
    if (ret != IRLIB_SUCCESS)
    {
        printf("fail to start preview, ret is %d\n", ret);
        g_ir_video_handle->ir_video_release(g_iruvc_handle, &dev_param);
        g_ir_video_handle->ir_video_close(g_iruvc_handle);
        goto fail;
    }

    // Set the parameters of the uvc video stream
    video_params.usr_callback.iruvc_handle = g_iruvc_handle;
    video_params.usr_callback.usr_func = NULL;
    video_params.usr_callback.usr_param = NULL;
    video_params.camera_param.width = width;
    video_params.camera_param.height = image_height + temp_height + info_height + dummy_height;
    video_params.camera_param.frame_size = g_raw_byte_size;
    video_params.camera_param.fps = output_fps;
    video_params.camera_param.timeout_ms_delay = 2000;
    video_params.camera_param.format = output_type;
    stop_params.stop_mode = CLOSE_CAM_SIDE_PREVIEW;

    ret = g_ir_video_handle->ir_video_start_stream(g_iruvc_handle, &video_params);
    if (ret != IRLIB_SUCCESS)
    {
        printf("fail to start video stream, ret is %d\n", ret);
        g_ir_video_handle->ir_video_release(g_iruvc_handle, &dev_param);
        g_ir_video_handle->ir_video_close(g_iruvc_handle);
        goto fail;
    }
    printf("raw size is %d\n", g_raw_byte_size);

    if (g_raw_byte_size <= 0)
    {
        printf("raw_byte_size error. size is %d", g_raw_byte_size);
        g_ir_video_handle->ir_video_stop_stream(g_iruvc_handle, &stop_params);
        g_ir_video_handle->ir_video_release(g_iruvc_handle, &dev_param);
        g_ir_video_handle->ir_video_close(g_iruvc_handle);
        goto fail;
    }
    g_raw_frame_data = (uint8_t*) malloc(g_raw_byte_size);
    if (g_raw_frame_data == NULL)
    {
        printf("malloc failed for raw_frame_data\n");
        g_ir_video_handle->ir_video_stop_stream(g_iruvc_handle, &stop_params);
        g_ir_video_handle->ir_video_release(g_iruvc_handle, &dev_param);
        g_ir_video_handle->ir_video_close(g_iruvc_handle);
        goto fail;
    }

    g_is_running = 1;
    printf("IRAY mini2 process started successfully, entering frame loop\n");
    //basic_palette_idx_set(g_ircmd_handle, 0);

    // 主循环：持续获取帧并发送到VPP
    while (g_is_running)
    {
        ret = g_ir_video_handle->ir_video_frame_get(g_iruvc_handle, NULL, g_raw_frame_data, g_raw_byte_size);
        if (ret == IRLIB_SUCCESS)
        {
            // 调用回调函数发送帧数据
            if (g_frame_callback != NULL)
            {
                g_frame_callback(g_raw_frame_data, g_raw_byte_size);
            }
        }
        else if (ret == IRUVC_GET_FRAME_TIMEOUT)
        {
            // 超时，继续尝试
            usleep(1000); // 1ms
        }
        else
        {
            printf("ir_video_frame_get failed, ret is %d\n", ret);
            usleep(10000); // 10ms
        }
    }

    // Cleanup process
    g_ir_video_handle->ir_video_stop_stream(g_iruvc_handle, &stop_params);
    g_ir_video_handle->ir_video_release(g_iruvc_handle, &dev_param);
    g_ir_video_handle->ir_video_close(g_iruvc_handle);

fail:
    if (g_raw_frame_data != NULL)
    {
        free(g_raw_frame_data);
        g_raw_frame_data = NULL;
    }
    if (g_ircmd_handle != NULL)
    {
        ircmd_delete_handle(g_ircmd_handle);
        g_ircmd_handle = NULL;
    }
    if (g_ir_control_handle != NULL)
    {
        ir_control_handle_delete(&g_ir_control_handle);
    }
    if (g_iruvc_handle != NULL)
    {
        iruvc_camera_handle_delete(g_iruvc_handle);
        g_iruvc_handle = NULL;
    }
    if (g_ir_video_handle != NULL)
    {
        ir_video_handle_delete(&g_ir_video_handle);
    }

    printf("start_iray_proc: Process completed\n");
    return ret == IRLIB_SUCCESS ? 0 : -1;
}
#endif

int start_iray_proc(void *callback)
{
    printf("uart cmd and usb image sample start\n");

    // config config_obj;
    // char* config_path;
    // if (argc != 2)
    // {
    //     printf("usage: %s <path_of_config_file>\n", argv[0]);
    //     return -1;
    // }

    // config_path = argv[1];

    // if (config_obj.parse_config(config_path) != 0)
    // {
    //     printf("parse config failed\n");
    //     return -1;
    // }

    // single_config product_config;
    // if (false == config_obj.get_config(product_config))
    // {
    //     printf("can't find config\n");
    //     return -1;
    // }

    printf("Depends lib version:\n");
    printf("version:%s\n", ircmd_version());
    printf("version:%s\n", ircam_version());
    printf("version:%s\n", iruart_version());
    printf("version:%s\n", iruvc_version());

    printf("set libs log level:\n");
    ircmd_log_register(IRCMD_LOG_ERROR,NULL,NULL);
    iruart_log_register(IRUART_LOG_DEBUG,NULL,NULL);
    ircam_log_register(IRCAM_LOG_ERROR,NULL, NULL);
    iruvc_log_register(IRUVC_LOG_ERROR,NULL,NULL);

    printf("init all stream handle\n");
    int fd;
    int ret;
    StreamFrameInfo_t stream_frame_info;
    memset(&stream_frame_info, 0, sizeof(stream_frame_info) - sizeof(product_config));
    stream_frame_info.product_config = product_config;

    printf("init uvc stream handle\n");
    ir_video_handle_create(&ir_image_video_handle);
    iruvc_handle = (IruvcHandle_t*)iruvc_camera_handle_create(ir_image_video_handle);//创建uvc_handle
    stream_frame_info.image_driver_handle = iruvc_handle;
    IruvcDevParam_t dev_param;
    memset(&dev_param, 0, sizeof(IruvcDevParam_t));

    dev_param.pid = product_config.camera.uvc_stream_conf.dev_info.pid;
    dev_param.vid = product_config.camera.uvc_stream_conf.dev_info.vid;
    dev_param.same_idx = product_config.camera.uvc_stream_conf.dev_info.same_id;

    stream_frame_info.image_dev_params = &dev_param;
    ret = ir_image_video_handle->ir_video_open(stream_frame_info.image_driver_handle, &dev_param);
    printf("open video : %d\n", ret);
    ret = ir_image_video_handle->ir_video_init(stream_frame_info.image_driver_handle, &dev_param);
    printf("init video : %d\n", ret);
    IruvcCamStreamParams_t params;
    memset(&params, 0, sizeof(params));
    stream_frame_info.stream_config = &params;
    
    printf("init control handle\n");
    IrControlHandle_t* ir_control_handle = NULL;
    ir_control_handle_create(&ir_control_handle);
    if (product_config.control.is_usb_control || product_config.control.is_i2c_usb_control)
    {
        if (product_config.control.is_usb_control)
        {
            iruvc_usb_handle_create_with_exist_instance(ir_control_handle, iruvc_handle);
        }
        else
        {
            iruvc_i2c_usb_handle_create_with_exist_instance(ir_control_handle, iruvc_handle);
        }

        do
        {
            if (dev_param.pid == product_config.control.usb_param.pid
                && dev_param.vid == product_config.control.usb_param.vid
                && dev_param.same_idx == product_config.control.usb_param.same_id)
            {
                break;
            }
            IruvcDevParam_t control_dev_param;
            memset(&control_dev_param, 0, sizeof(IruvcDevParam_t));
            control_dev_param.pid = product_config.control.usb_param.pid;
            control_dev_param.vid = product_config.control.usb_param.vid;
            control_dev_param.same_idx = product_config.control.usb_param.same_id;
            ret = ir_control_handle->ir_control_open(iruvc_handle, &control_dev_param);
            printf("open control node: %d\n", ret);
        } while (0);

        stream_frame_info.ircmd_handle = ircmd_create_handle(ir_control_handle);
    }
    else if (product_config.control.is_uart_control)
    {
        IruartHandle_t* iruart_handle = NULL;
        iruart_handle = iruart_handle_create(ir_control_handle);
        IrcmdHandle_t* cmd_handle = NULL;
        ret = search_com(ir_control_handle, &cmd_handle, product_config.control.uart_param.com_index);
        if (ret != 0)
        {
            printf("fail to find available com\n");
            return -1;
        }
        stream_frame_info.ircmd_handle = cmd_handle;
    }

    load_stream_frame_info(&stream_frame_info, true, true);
    init_pthread_sem();
    pthread_t stream_thread,display_thread,capture_thread,cmd_thread;
    pthread_create(&stream_thread, NULL, uvc_stream_function, &stream_frame_info);
    //pthread_create(&display_thread, NULL, opencv_display_function, &stream_frame_info);
    //pthread_create(&capture_thread, NULL, capture_function, &stream_frame_info);
    pthread_create(&cmd_thread, NULL, cmd_function, &stream_frame_info);
    sleep(1);

    printf("in streaming\n");
    void *thread_result;
    pthread_join(stream_thread, &thread_result);
    printf("stop stream\n");
    //pthread_cancel(display_thread);
    //pthread_cancel(cmd_thread);
    destroy_pthread_sem();
    destroy_data_demo(&stream_frame_info);

    if (product_config.control.is_usb_control || product_config.control.is_i2c_usb_control)
    {
        ircmd_delete_handle(stream_frame_info.ircmd_handle);
        stream_frame_info.ircmd_handle = NULL;
        ir_control_handle->ir_control_release(ir_control_handle->ir_control_handle, NULL);
        ir_control_handle->ir_control_close(ir_control_handle->ir_control_handle);
        if (product_config.control.is_usb_control)
        {
            iruvc_usb_handle_delete(iruvc_handle);
        }
        else
        {
            iruvc_i2c_usb_handle_delete(iruvc_handle);
        }
    }
    else if (product_config.control.is_uart_control)
    {
        ircmd_delete_handle(stream_frame_info.ircmd_handle);
        stream_frame_info.ircmd_handle = NULL;
        ir_control_handle->ir_control_release(ir_control_handle->ir_control_handle, NULL);
        ir_control_handle->ir_control_close(ir_control_handle->ir_control_handle);
        iruart_handle_delete(ir_control_handle);
    }

    ir_control_handle_delete(&ir_control_handle);
    ir_control_handle = NULL;
    ir_image_video_handle->ir_video_release(stream_frame_info.image_driver_handle, &dev_param);
    ir_image_video_handle->ir_video_close(stream_frame_info.image_driver_handle);
    iruvc_camera_handle_delete(iruvc_handle);
    iruvc_handle = NULL;
    ir_video_handle_delete(&ir_image_video_handle);
    ir_image_video_handle = NULL;

    return 0;
}



// 停止IRAY处理的函数
int stop_iray_proc(void)
{
    printf("stop_iray_proc: Stopping IRAY mini2 process\n");
    g_is_running = 0;
    return 0;
}
start_iray_proc