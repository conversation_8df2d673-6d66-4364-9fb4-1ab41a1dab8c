#ifndef YUV_QUEUE_H
#define YUV_QUEUE_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <unistd.h>


// 最大队列长度
#define QUEUE_CAPACITY 5

// 一个 YUV 帧的结构
typedef struct {
    unsigned char *data;  // 指向拷贝后的 YUV 数据
    int length;           // 数据长度
    int width;            // 图像宽度
    int height;           // 图像高度
} yuv_frame_t;

// 线程安全队列结构
typedef struct {
    yuv_frame_t  buf[QUEUE_CAPACITY];
    int          head;      // 下一个要 pop 的索引
    int          tail;      // 下一个要 push 的索引
    int          size;      // 当前队列中帧数
    pthread_mutex_t  mutex;
    pthread_cond_t   not_empty;
} yuv_queue_t;


int start_yuv_queue(void);
int stop_yuv_queue(void);

yuv_frame_t *yuv_queue_pop();
void yuv_queue_push(unsigned char *src, int length, int width, int height);

#endif