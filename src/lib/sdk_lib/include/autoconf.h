/*
  Copyright (c), 2001-2022, Shenshu Tech. Co., Ltd.
 */
#ifndef __AUTOCONF_H__
#define __AUTOCONF_H__

#define AUTOCONF_TIMESTAMP "2022-12-28 11:55:55 CST"

/*
 * General Setup
 */
#define CONFIG_SS928V100 1
#define CONFIG_OT_CHIP_TYPE 0x01090100
#define CONFIG_OT_ARCH "ss928v100"
#define CONFIG_SUBCHIP_SS928V100 1
#define CONFIG_OT_SUBARCH "ss928v100"
#define CONFIG_OT_SUBCHIP_TYPE 0x01090100
#define CONFIG_SMP 1
#define CONFIG_ARM_ARCH_TYPE "smp"
#define CONFIG_A55 1
#define CONFIG_CPU_TYPE "a55"
#define CONFIG_VERSION_ASIC 1
#define CONFIG_OT_FPGA "n"
#define CONFIG_LINUX_OS 1
#define CONFIG_OS_TYPE "linux"
#define CONFIG_PHYS_ADDR_BIT_WIDTH_64 1
#define CONFIG_LINUX_4_19_y 1
#define CONFIG_KERNEL_VERSION "linux-4.19.y"
#define CONFIG_KERNEL_AARCH64_MIX210 1
#define CONFIG_OT_CROSS "aarch64-mix210-linux-"
#define CONFIG_LIBC_TYPE "glibc"
#define CONFIG_KERNEL_BIT "KERNEL_BIT_64"
#define CONFIG_USER_AARCH64_MIX210 1
#define CONFIG_OT_CROSS_LIB "aarch64-mix210-linux-"
#define CONFIG_USER_BIT "USER_BIT_64"
#define CONFIG_LINUX_STYLE 1
#define CONFIG_CODE_SYTLE "USE_LINUX_STYLE"
#define CONFIG_RELEASE_TYPE_RELEASE 1
#define CONFIG_OT_RLS_MODE "OT_RELEASE"
#define CONFIG_OT_PLATFORM_V8 1

/*
 * Media Modules Setup
 */

/*
 * media audio config
 */
#define CONFIG_OT_AUDIO_SUPPORT 1
#define CONFIG_OT_ACODEC_SUPPORT 1
#define CONFIG_OT_ACODEC_VERSION "RemixV100"
#define CONFIG_OT_ACODEC_MAX_GAIN 56
#define CONFIG_OT_ACODEC_MIN_GAIN 0
#define CONFIG_OT_ACODEC_GAIN_STEP 3
#define CONFIG_OT_ACODEC_FAST_POWER_SUPPORT 1
#define CONFIG_OT_DOWNVQE_SUPPORT 1
#define CONFIG_OT_TALKVQE_SUPPORT 1
#define CONFIG_OT_RECORDVQE_SUPPORT 1
#define CONFIG_OT_TALKVQEV2_SUPPORT 1
#define CONFIG_OT_TALKVQEV2_WNR_SUPPORT 1
#define CONFIG_OT_AUDIO_STATIC_REGISTER_SUPPORT 1

/*
 * media base config
 */
#define CONFIG_OT_VB_EXTPOOL_SUPPORT 1
#define CONFIG_OT_VB_SUPPLEMENT_MASK_SUPPORT 1
#define CONFIG_OT_VB_ASYNC_SUPPORT 1

/*
 * media chnl config
 */
#define CONFIG_OT_CHNL_SUPPORT 1

/*
 * media dis config
 */
#define CONFIG_OT_DIS_SUPPORT 1
#define CONFIG_OT_DIS_SUBMIT_TO_VGS_SUPPORT 1

/*
 * media gdc config
 */
#define CONFIG_OT_GDC_SUPPORT 1
#define CONFIG_OT_GDC_LOWDELAY_SUPPORT 1
#define CONFIG_OT_GDC_FISHEYE_LMF_SUPPORT 1
#define CONFIG_OT_GDC_FISHEYE_SUPPORT 1
#define CONFIG_OT_GDC_LDC_SUPPORT 1
#define CONFIG_OT_GDC_LDC_V3_SUPPORT 1
#define CONFIG_OT_GDC_SPREAD_SUPPORT 1

/*
 * media hdmi config
 */
#define CONFIG_OT_HDMI_SUPPORT 1

/*
 * media isp config
 */
#define CONFIG_OT_ISP_SUPPORT 1
#define CONFIG_OT_ISP_AF_SUPPORT 1
#define CONFIG_OT_ISP_CR_SUPPORT 1
#define CONFIG_OT_ISP_PREGAMMA_SUPPORT 1
#define CONFIG_OT_ISP_GCAC_SUPPORT 1
#define CONFIG_OT_ISP_CA_SUPPORT 1
#define CONFIG_OT_ISP_EDGEMARK_SUPPORT 1
#define CONFIG_OT_ISP_HLC_SUPPORT 1
#define CONFIG_OT_ISP_SPECAWB_SUPPORT 1
#define CONFIG_OT_ISP_DPC_STATIC_TABLE_SUPPORT 1
#define CONFIG_OT_PQP_SUPPORT 1
#define CONFIG_OT_ISP_HNR_SUPPORT 1

/*
 * media region config
 */
#define CONFIG_OT_REGION_SUPPORT 1
#define CONFIG_OT_RGN_CORNER_RECT_SUPPORT 1

/*
 * media sys config
 */
#define CONFIG_OT_SYS_SUPPORT 1
#define CONFIG_OT_SYS_SCALE_COEF_SUPPORT 1
#define CONFIG_OT_SYS_SCALE_COEF_ONLINE_SUPPORT 1
#define CONFIG_OT_SYS_SMMU_SUPPORT 1

/*
 * media tde config
 */
#define CONFIG_OT_TDE_SUPPORT 1

/*
 * media vda config
 */

/*
 * media vdec config
 */
#define CONFIG_OT_VDEC_SUPPORT 1
#define CONFIG_OT_H265D_SUPPORT 1
#define CONFIG_OT_H264D_SUPPORT 1
#define CONFIG_OT_JPEGD_SUPPORT 1
#define CONFIG_OT_DEC_SHVC_SUPPORT 1
#define CONFIG_VDEC_IP "VDEC_IP_VDH"
#define CONFIG_OT_JPEGD_SUPPORT 1
#define CONFIG_OT_JPEGD_PROGRESSIVE 1
#define CONFIG_VDEC_ROTATION_SUPPORT 1
#define CONFIG_VDEC_USERPIC_SUPPORT 1
#define CONFIG_VDEC_USERDATA_SUPPORT 1
#define CONFIG_VDEC_LOWDELAY_SUPPORT 1

/*
 * media venc config
 */
#define CONFIG_OT_VENC_SUPPORT 1
#define CONFIG_OT_H265E_SUPPORT 1
#define CONFIG_OT_H265E_USERDATA_SUPPORT 1
#define CONFIG_OT_H265E_INTRA_REFRESH_SUPPORT 1
#define CONFIG_OT_H264E_SUPPORT 1
#define CONFIG_OT_H264E_USERDATA_SUPPORT 1
#define CONFIG_OT_H264E_INTRA_REFRESH_SUPPORT 1
#define CONFIG_OT_JPEGE_SUPPORT 1
#define CONFIG_OT_JPEGE_ROI_SUPPORT 1
#define CONFIG_OT_MJPEGE_SUPPORT 1
#define CONFIG_OT_JPEGE_MPF_DCF_SUPPORT 1
#define CONFIG_OT_JPEGE_USERDATA_SUPPORT 1
#define CONFIG_OT_VENC_LOWDELAY_SUPPORT 1
#define CONFIG_OT_VENC_VPSSAUTO_SUPPORT 1
#define CONFIG_OT_VENC_FRAMEBUF_RECYCLE_SUPPORT 1
#define CONFIG_OT_VENC_VGS_SUPPORT 1
#define CONFIG_OT_VENC_SVC_SUPPORT 1
#define CONFIG_OT_VENC_SMARTP_SUPPORT 1
#define CONFIG_OT_VENC_DUALP_SUPPORT 1
#define CONFIG_OT_VENC_RCNREF_SHARE_SUPPORT 1
#define CONFIG_OT_VENC_DEBREATH_SUPPORT 1
#define CONFIG_OT_VENC_SKIPREF_SUPPORT 1
#define CONFIG_OT_VENC_SCENE0_SUPPORT 1
#define CONFIG_OT_VENC_SCENE1_SUPPORT 1
#define CONFIG_OT_VENC_SCENE2_SUPPORT 1
#define CONFIG_OT_RC_AVBR_SUPPORT 1
#define CONFIG_OT_RC_QPMAP_SUPPORT 1
#define CONFIG_OT_RC_QVBR_SUPPORT 1
#define CONFIG_OT_RC_CVBR_SUPPORT 1
#define CONFIG_OT_VENC_COMPOSITE_SUPPORT 1

/*
 * media vgs config
 */
#define CONFIG_OT_VGS_SUPPORT 1
#define CONFIG_OT_VGS_STITCH_SUPPORT 1
#define CONFIG_OT_VGS_LUMA_STAT_SUPPORT 1
#define CONFIG_OT_VGS_CORNER_RECT_SUPPORT 1
#define CONFIG_OT_VGS_SHBD_SUPPORT 1
#define CONFIG_OT_VGS_MCF_SUPPORT 1
#define CONFIG_OT_VGS_LOW_DELAY_SUPPORT 1
#define CONFIG_OT_VGS_FPD_SUPPORT 1
#define CONFIG_OT_VGS_MULTI_CHN_SUPPORT 1
#define CONFIG_OT_VGS_GME_SUPPORT 1
#define CONFIG_OT_VGS_MOSAIC_ONLINE_SUPPORT 1
#define CONFIG_OT_VGS_USER_ONLINE_SUPPORT 1

/*
 * media vi config
 */
#define CONFIG_OT_VI_SUPPORT 1
#define CONFIG_OT_VI_ALL_SUPPORT 1
#define CONFIG_OT_VI_DEV_BAS 1
#define CONFIG_OT_VI_DEV_SEND_FRAME 1
#define CONFIG_OT_VI_DEV_GENERATE_TIMING 1
#define CONFIG_OT_VI_DEV_GENERATE_DATA 1
#define CONFIG_OT_VI_VIRT_PIPE 1
#define CONFIG_OT_VI_PIPE_PRE_CROP 1
#define CONFIG_OT_VI_PIPE_POST_CROP 1
#define CONFIG_OT_VI_PIPE_DUMP_FRAME 1
#define CONFIG_OT_VI_PIPE_DUMP_PRIVATE_DATA 1
#define CONFIG_OT_VI_PIPE_BAS 1
#define CONFIG_OT_VI_PIPE_SEND_FRAME 1
#define CONFIG_OT_VI_PIPE_INTERRUPT_EN 1
#define CONFIG_OT_VI_PIPE_LOW_DELAY 1
#define CONFIG_OT_VI_PIPE_FRAME_INTERRUPT_TYPE 1
#define CONFIG_OT_VI_PIPE_GET_COMPRESS_PARAM 1
#define CONFIG_OT_VI_PIPE_USER_PIC 1
#define CONFIG_OT_VI_PIPE_FPN 1
#define CONFIG_OT_VI_PIPE_IR_ASSIST_NR 1
#define CONFIG_OT_VI_PIPE_BNR 1
#define CONFIG_OT_VI_PIPE_HNR 1
#define CONFIG_OT_VI_CHN_LOW_DELAY 1
#define CONFIG_OT_VI_CHN_DIS 1
#define CONFIG_OT_VI_CHN_ROTATION 1
#define CONFIG_OT_VI_CHN_LDC 1
#define CONFIG_OT_VI_CHN_SPREAD 1
#define CONFIG_OT_VI_CHN_RGN_LUMA 1
#define CONFIG_OT_VI_CHN_FOV_CORRECTION 1
#define CONFIG_OT_VI_CHN_FISHEYE 1
#define CONFIG_OT_VI_EXT_CHN 1
#define CONFIG_OT_VI_STITCH_GRP 1
#define CONFIG_OT_VI_PTS 1

/*
 * media vo config
 */
#define CONFIG_OT_VO_SUPPORT 1
#define CONFIG_OT_VO_HD 1
#define CONFIG_OT_VO_VPSS_AUTO 1
#define CONFIG_OT_VO_PLAY_CTL 1
#define CONFIG_OT_VO_LUMA 1
#define CONFIG_OT_VO_COVER_OSD_SUPPORT 1
#define CONFIG_OT_VO_CORNER_RECT_SUPPORT 1
#define CONFIG_OT_VO_VIRTDEV_SUPPORT 1
#define CONFIG_OT_VO_VGS 1
#define CONFIG_OT_VO_GRAPH 1
#define CONFIG_OT_VO_BATCH 1
#define CONFIG_OT_VO_LOW_DELAY 1
#define CONFIG_OT_VO_BORDER_BY_COVER 1
#define CONFIG_OT_VO_CVBS 1
#define CONFIG_OT_VO_RGB 1
#define CONFIG_OT_VO_BT1120 1
#define CONFIG_OT_VO_MIPI 1
#define CONFIG_OT_VO_HDMI 1
#define CONFIG_OT_VO_DEV_BYPASS 1
#define CONFIG_OT_VO_EXPORT_FUNCTION 1
#define CONFIG_OT_VO_FB_SEPARATE 1

/*
 * media vpss config
 */
#define CONFIG_OT_VPSS_SUPPORT 1
#define CONFIG_OT_VPSS_ONLINE_SUPPORT 1
#define CONFIG_OT_VPSS_SLAVE_SUPPORT 1
#define CONFIG_OT_VPSS_3DNR_SUPPORT 1
#define CONFIG_OT_VPSS_3DNR_GAMMA_SUPPORT 1
#define CONFIG_OT_VPSS_3DNR_BNR_LINKAGE_SUPPORT 1
#define CONFIG_OT_VPSS_3DNR_DELAY_MODE_SUPPORT 1
#define CONFIG_OT_VPSS_AUTO_SUPPORT 1
#define CONFIG_OT_VPSS_BACKUP_SUPPORT 1
#define CONFIG_OT_VPSS_COVER_SUPPORT 1
#define CONFIG_OT_VPSS_MOSAIC_SUPPORT 1
#define CONFIG_OT_VPSS_DELAY_SUPPORT 1
#define CONFIG_OT_VPSS_2SCALE_SUPPORT 1
#define CONFIG_OT_VPSS_VGS_GRP_SUPPORT 1
#define CONFIG_OT_VPSS_BUFFER_REUSE_SUPPORT 1
#define CONFIG_OT_VPSS_LOW_DELAY_SUPPORT 1
#define CONFIG_OT_VPSS_IPC_SUPPORT 1
#define CONFIG_OT_VPSS_EXT_CHN_SUPPORT 1
#define CONFIG_OT_VPSS_CHN_CROP_SUPPORT 1
#define CONFIG_OT_VPSS_MCF_SUPPORT 1
#define CONFIG_OT_VPSS_CHN_LDC_SUPPORT 1
#define CONFIG_OT_VPSS_CHN_SPREAD_SUPPORT 1
#define CONFIG_OT_VPSS_2DOF_SUPPORT 1

/*
 * media avs config
 */
#define CONFIG_OT_AVS_SUPPORT 1

/*
 * media vpp config
 */
#define CONFIG_OT_VPP_SUPPORT 1
#define CONFIG_OT_VPP_FOV_CORRECTION_SUPPORT 1
#define CONFIG_OT_VPP_BNR_SUPPORT 1
#define CONFIG_OT_VPP_VI_SUPPORT 1
#define CONFIG_OT_VPP_COVEREX_RATIO_SUPPORT 1

/*
 * media dcc config
 */

/*
 * media uvc config
 */
#define CONFIG_OT_UVC_SUPPORT 1

/*
 * Device Driver Setup
 */

/*
 * drv config
 */
#define CONFIG_DRV 1
#define CONFIG_EXTDRV 1
#define CONFIG_INTERDRV 1
#define CONFIG_OT_USER 1
#define CONFIG_MIPI_TX 1
#define CONFIG_MIPI_RX 1
#define CONFIG_OT_IR 1
#define CONFIG_OT_LSADC 1
#define CONFIG_OT_WDG 1
#define CONFIG_OT_SYSCFG 1

/*
 * Component Setup
 */

/*
 * Component security_subsys Config
 */
#define CONFIG_OT_SECURITY_SUBSYS_SUPPORT 1
#define CONFIG_OT_CIPHER_SUPPORT 1
#define CONFIG_OT_OTP_SUPPORT 1
#define CONFIG_OT_KLAD_SUPPORT 1

/*
 * Component gfbg Config
 */
#define CONFIG_OT_GFBG_SUPPORT 1
#define CONFIG_GFBG_DPU_V3 1

/*
 * Component ot_syslink Config
 */

/*
 * Component pciv Config
 */
#define CONFIG_OT_PCIV_SUPPORT 1

/*
 * Component avs lut Config
 */
#define CONFIG_OT_AVS_LUT_SUPPORT 1

/*
 * Component avs convert Config
 */
#define CONFIG_OT_AVS_CONVERT_SUPPORT 1

/*
 * Component pos_query Config
 */
#define CONFIG_OT_POS_QUERY_SUPPORT 1

/*
 * Component snap Config
 */
#define CONFIG_OT_SNAP_SUPPORT 1

/*
 * Component photo Config
 */
#define CONFIG_OT_PHOTO_SUPPORT 1

/*
 * Component heif Config
 */
#define CONFIG_OT_HEIF_SUPPORT 1

/*
 * Component svp Config
 */
#define CONFIG_OT_SVP_SUPPORT 1
#define CONFIG_OT_SVP_DSP 1
#define CONFIG_OT_SVP_LITEOS 1
#define CONFIG_OT_SVP_IVE 1
#define CONFIG_OT_SVP_IVE_CSC 1
#define CONFIG_OT_SVP_IVE_FILTER_AND_CSC 1
#define CONFIG_OT_SVP_IVE_NCC 1
#define CONFIG_OT_SVP_IVE_LBP 1
#define CONFIG_OT_SVP_IVE_NORM_GRAD 1
#define CONFIG_OT_SVP_IVE_ST_CANDI_CORNER 1
#define CONFIG_OT_SVP_IVE_RESIZE 1
#define CONFIG_OT_SVP_IVE_PERSP_TRANS 1
#define CONFIG_OT_SVP_IVE_KCF 1
#define CONFIG_OT_SVP_IVE_HOG 1
#define CONFIG_OT_SVP_MD 1
#define CONFIG_OT_SVP_MAU 1
#define CONFIG_OT_SVP_NPU_V1R1 1
#define CONFIG_OT_SVP_NPU_V1R1_COMPILE 1
#define CONFIG_OT_SVP_NPU_V2R1 1
#define CONFIG_OT_SVP_DPU_RECT 1
#define CONFIG_OT_SVP_DPU_MATCH 1

/*
 * Component motionfusion config
 */
#define CONFIG_OT_MOTIONFUSION_SUPPORT 1

/*
 * Component mcf Config
 */
#define CONFIG_OT_MCF_SUPPORT 1

/*
 * Component securec Config
 */
#define CONFIG_OT_SECUREC_SUPPORT 1

/*
 * Debug Config
 */
#define CONFIG_OT_GDB_NO 1
#define CONFIG_OT_GDB "n"
#define CONFIG_OT_PROC_SHOW_SUPPORT 1
#define CONFIG_OT_LOG_TRACE_SUPPORT 1
#define CONFIG_OT_LOG_TRACE_ALL 1
#define CONFIG_OT_LOG_TRACE_LEVEL 7

/*
 * Test Config
 */

#endif /* __AUTOCONF_H__ */

