// yuv_queue_example.c
#include "yuv_queue.h"



// 全局队列
static yuv_queue_t g_queue;
static int exit_flag = 0;


// 初始化队列
void yuv_queue_init(yuv_queue_t *q) {
    q->head = q->tail = q->size = 0;
    pthread_mutex_init(&q->mutex, NULL);
    pthread_cond_init(&q->not_empty, NULL);
}

// 清理队列中的剩余帧（释放内存）
void yuv_queue_destroy(yuv_queue_t *q) {
    pthread_mutex_lock(&q->mutex);
    for (int i = 0; i < q->size; i++) {
        int idx = (q->head + i) % QUEUE_CAPACITY;
        free(q->buf[idx].data);
    }
    pthread_mutex_unlock(&q->mutex);

    pthread_mutex_destroy(&q->mutex);
    pthread_cond_destroy(&q->not_empty);
}

// 向队列中推入一帧；若队列已满，丢弃最旧帧
void queue_push(yuv_queue_t *q, unsigned char *src, int length, int width, int height) {
    unsigned char *copy = malloc(length);
    if (!copy) {
        fprintf(stderr, "Failed to allocate memory for YUV frame\n");
        return;
    }
    memcpy(copy, src, length);

    pthread_mutex_lock(&q->mutex);

    // 如果已满，释放最旧帧的内存，并丢弃它
    if (q->size == QUEUE_CAPACITY) {
        yuv_frame_t *old = &q->buf[q->head];
        free(old->data);
        q->head = (q->head + 1) % QUEUE_CAPACITY;
        q->size--;
    }

    // 在 tail 处插入新帧
    yuv_frame_t *dst = &q->buf[q->tail];
    dst->data   = copy;
    dst->length = length;
    dst->width  = width;
    dst->height = height;

    q->tail = (q->tail + 1) % QUEUE_CAPACITY;
    q->size++;

    // 通知可能在等待数据的消费者
    pthread_cond_signal(&q->not_empty);
    pthread_mutex_unlock(&q->mutex);
}

// 从队列中阻塞式地取出一帧，返回已分配的 yuv_frame_t*（处理后需 free frame->data 及 frame 本身）
yuv_frame_t *queue_pop(yuv_queue_t *q) {
    pthread_mutex_lock(&q->mutex);
    while (q->size == 0) {
        // 队列空，等待新数据到来
        pthread_cond_wait(&q->not_empty, &q->mutex);
    }

    // 取出 head 指向的帧
    yuv_frame_t *frame = malloc(sizeof(yuv_frame_t));
    if (!frame) {
        fprintf(stderr, "Failed to allocate memory for frame struct\n");
        pthread_mutex_unlock(&q->mutex);
        return NULL;
    }
    yuv_frame_t *src = &q->buf[q->head];
    frame->length = src->length;
    frame->width  = src->width;
    frame->height = src->height;
    frame->data   = src->data;  // 直接接管内存

    // 清空队列中的这项
    q->head = (q->head + 1) % QUEUE_CAPACITY;
    q->size--;

    pthread_mutex_unlock(&q->mutex);
    return frame;
}

yuv_frame_t *yuv_queue_pop()
{
    return queue_pop(&g_queue);
}

void yuv_queue_push(unsigned char *src, int length, int width, int height)
{
    queue_push(&g_queue, src, length, width, height);
}

// // 模拟回调函数，生产者将 YUV 数据推入队列
// void YUVDataCallback(unsigned char* pYUVBuffer, int nYUVBufLen,
//                      unsigned char* pRAWBuffer, int nRawBufLen,
//                      int nWidth, int nHeight, unsigned long dwUser)
// {
//     if (pYUVBuffer && nYUVBufLen > 0) {
//         // 只处理 YUV 数据，将其推入队列
//         yuv_queue_push(&g_queue, pYUVBuffer, nYUVBufLen, nWidth, nHeight);
//     }
// }

// 消费线程：不断从队列中取帧并处理
// void *consumer_thread(void *arg) {
//     (void)arg;
//     while (1) {
//         yuv_frame_t *frame = yuv_queue_pop(&g_queue);
//         if (!frame) continue;

//         // 在这里对 frame->data 进行耗时处理
//         printf("Consumer: got frame %dx%d, %d bytes\n",
//                frame->width, frame->height, frame->length);

//         // 处理完毕，释放内存
//         free(frame->data);
//         free(frame);
//     }
//     return NULL;
// }

int start_yuv_queue(void) {
    // 初始化队列
    yuv_queue_init(&g_queue);
}


// int start_yuv_queue_proc(void) {
    

//     // 初始化队列
//     yuv_queue_init(&g_queue);

//     // // 启动消费线程
//     // if (pthread_create(&tid, NULL, consumer_thread, NULL) != 0) {
//     //     perror("pthread_create");
//     //     return EXIT_FAILURE;
//     // }

//     // // 下面是模拟生产者不断调用回调，你的实际回调环境下无需这段模拟
//     // for (int i = 0; i < 20; i++) {
//     //     int width = 640, height = 480;
//     //     int len = width * height * 3 / 2;  // 假设 YUV420p
//     //     unsigned char *buf = malloc(len);
//     //     memset(buf, i, len);
//     //     YUVDataCallback(buf, len, NULL, 0, width, height, 0);
//     //     free(buf);
//     //     usleep(100000); // 100ms
//     // }

//     // while (!exit_flag)
//     // {
//     //     usleep(10000);
//     // }
    

//     // pthread_cancel(tid);
//     // pthread_join(tid, NULL);
//     // yuv_queue_destroy(&g_queue);
//     return 0;
// }


int stop_yuv_queue(void)
{
    yuv_queue_destroy(&g_queue);
    return 0;
}



