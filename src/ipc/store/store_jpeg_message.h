#ifndef STORE_JPEG_MESSAGE_H_
#define STORE_JPEG_MESSAGE_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "stdio.h"
#include "airvisen_message.h"
void airvisen_start_store_jpeg_ipc();
void airvisen_stop_store_jpeg_ipc();

void airvisen_start_store_jpeg_client_ipc();
void airvisen_send_start_capture_command();
void airvisen_send_stop_capture_command();
int airvisen_recv_store_jpeg_message(char *data, int data_len);

#ifdef __cplusplus
}
#endif
#endif /*STORE_JPEG_MESSAGE_H_*/