#ifndef INFRA_MESSAGE_H_
#define INFRA_MESSAGE_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "stdio.h"
#include "airvisen_message.h"


// typedef enum{
//     INFRA_SET_COLOR,
//     INFRA_REPORT_TEMPERATURE,
//     INFRA_GET_BRIGHTNESS,
//     INFRA_GET_BRIGHTNESS_ACK,
//     INFRA_SET_BRIGHTNESS,
//     INFRA_SET_BRIGHTNESS_ACK,
//     INFRA_GET_CONTRAST,
//     INFRA_GET_CONTRAST_ACK,
//     INFRA_SET_CONTRAST,
//     INFRA_SET_CONTRAST_ACK,
//     INFRA_GET_SHARPNESS,
//     INFRA_GET_SHARPNESS_ACK,
//     INFRA_SET_SHARPNESS,
//     INFRA_SET_SHARPNESS_ACK,
//     INFRA_GET_LCEVALUE,
//     INFRA_GET_LCEVALUE_ACK,
//     INFRA_SET_LCEVALUE,
//     INFRA_SET_LCEVALUE_ACK,
//     INFRA_GET_GAINAUTO,
//     INFRA_GET_GAINAUTO_ACK,
//     INFRA_SET_GAINAUTO,
//     INFRA_SET_GAINAUTO_ACK,
//     INFRA_GET_GAINMODEL,
//     INFRA_GET_GAINMODEL_ACK,
//     INFRA_SET_GAINMODEL,
//     INFRA_SET_GAINMODEL_ACK,
// }E_IR_COMMAND;

typedef enum{
    INFRA_CORRECT_SHUTTER = 0x0,
    INFRA_ADD_CONTRAST,
    INFRA_SUB_CONTRAST,
    INFRA_ADD_BRIGHTNESS,
    INFRA_SUB_BRIGHTNESS,
    INFRA_RESET_BRI_CONTRAST_DEFAULT = 0x05,
    INFRA_BACKGROUND_COMPENSATION,
    INFRA_SET_GAINAUTO,
    INFRA_SET_COLOR,
    INFRA_ZOOM_STOP = 0x1a,
    INFRA_ZOOM_PLUS = 0x1b,
    INFRA_ZOOM_MINUS = 0x1c,
    INFRA_ZOOM_SET = 0x1d,
    INFRA_ZOOM_BIG_PLUS = 0x20,
    INFRA_ZOOM_BIG_MINUS = 0x21,
    INFRA_REPORT_TEMPERATURE = 0x40
}E_IR_CTRL_CMD;

/**
 * infra command
 */
typedef struct 
{
    airvisen_track_message_head_s head;

    int max_temperature;
    int min_temperature;
    int ave_temperature;

}airvisen_infra_measure_result_s;


typedef struct 
{
    /* data */
    int x;
    int y;
    int temp;
}temperature_info;


/**
 * infra command
 */
typedef struct 
{
    airvisen_track_message_head_s head;

    temperature_info max_temperature;
    temperature_info min_temperature;
    temperature_info ave_temperature;

}airvisen_infra_measure_temp_result_s;


typedef struct 
{
    airvisen_track_message_head_s head;

    /**
     * 0-白热，1-黑热，2-聚变，3-彩虹，4-金秋，5-午日，
     * 6-铁红 7-琥珀，8-玉石，9-夕阳，10-冰火，11-油画，
     * 12-石榴，13-翡翠，14-春，15-夏，16-秋，17-冬，18-
     * 热检测，19-极光
     */
    int color;

}airvisen_infra_set_color_s;



typedef struct 
{
    int cmd;
    int len;

}ir_control_head_s;

typedef struct 
{
    /* data */
    ir_control_head_s head;
    /**reserved */
    int value;
} airvisen_infra_ctrl_s;


typedef struct 
{
    /* data */
    airvisen_track_message_head_s head;
    /**reserved */
    int value;
} airvisen_get_brightness,
 airvisen_get_contrast,
 airvisen_get_sharpness,
 airvisen_get_lcevalue,
 airvisen_get_gainauto,
 airvisen_get_gainmodel,
 airvisen_set_brightness,
 airvisen_set_contrast,
 airvisen_set_sharpness,
 airvisen_set_lcevalue,
 airvisen_set_gainauto,
 airvisen_set_digtal_zoom,
 airvisen_set_gainmodel,
 airvesen_ir_struct;

void airvisen_start_infra_ipc();
void airvisen_stop_infra_ipc();

void airvisen_start_infra_client_ipc();
void airvisen_send_set_color_command(int color);
void airvisen_send_temperautre_command(int max_temperature, int min_temperature,int ave_temperature);
void airvisen_send_temperautre_point_command(int max_temperature, int max_x, int max_y, int min_temperature, int min_x, int min_y, int ave_temperature);
int airvisen_recv_infra_message(char *data, int data_len);
int airvisen_recv_infra_message_wait(char *data, int data_len);
void airvisen_send_ir_result(int cmd, int value);

#ifdef __cplusplus
}
#endif
#endif /*INFRA_MESSAGE_H_*/