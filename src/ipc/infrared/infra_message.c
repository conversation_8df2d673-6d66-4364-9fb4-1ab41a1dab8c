#include "ipc.h"
#include "stdio.h"
#include "infra_message.h"

static int sock;
#define INFRA_SOCKET_ADDRESS "ipc:///tmp/infra.protocol.ipc"

void airvisen_start_infra_ipc()
{
    sock = ipc_server_start_pair(INFRA_SOCKET_ADDRESS);

    if (sock < 0)
    {
        printf("start infra ipc server faild\n");
    }
}


void airvisen_start_infra_client_ipc()
{
    sock = ipc_client_start_pair(INFRA_SOCKET_ADDRESS);

    if (sock < 0)
    {
        printf("start infra ipc server faild\n");
    }
}

void airvisen_send_set_color_command(int color)
{
    airvisen_infra_set_color_s infra_msg = {0};
    infra_msg.head.cmd = INFRA_SET_COLOR;
    infra_msg.head.len = sizeof(airvisen_infra_set_color_s);
    infra_msg.color = color;
    ipc_send_message(sock, sizeof(airvisen_infra_set_color_s), (char *)&infra_msg);
}

void airvisen_send_temperautre_command(int max_temperature, int min_temperature,int ave_temperature)
{
    airvisen_infra_measure_result_s infra_msg = {0};
    infra_msg.head.cmd = INFRA_REPORT_TEMPERATURE;
    infra_msg.head.len = sizeof(airvisen_infra_measure_result_s);
    infra_msg.max_temperature = max_temperature;
    infra_msg.min_temperature = min_temperature;
    infra_msg.ave_temperature = ave_temperature;
    ipc_send_message(sock, sizeof(airvisen_infra_measure_result_s), (char *)&infra_msg);
}

void airvisen_send_temperautre_point_command(int max_temperature, int max_x, int max_y, int min_temperature, int min_x, int min_y, int ave_temperature)
{
    airvisen_infra_measure_temp_result_s infra_msg = {0};
    infra_msg.head.cmd = INFRA_REPORT_TEMPERATURE;
    infra_msg.head.len = sizeof(airvisen_infra_measure_temp_result_s);
    infra_msg.max_temperature.temp = max_temperature;
    infra_msg.max_temperature.x = max_x;
    infra_msg.max_temperature.y = max_y;

    infra_msg.min_temperature.temp = min_temperature;
    infra_msg.min_temperature.x = min_x;
    infra_msg.min_temperature.y = min_y;
    infra_msg.ave_temperature.temp = ave_temperature;
    
    ipc_send_message(sock, sizeof(airvisen_infra_measure_temp_result_s), (char *)&infra_msg);
}


void airvisen_send_ir_result(int cmd, int value)
{
    airvesen_ir_struct infra_msg = {0};
    infra_msg.head.cmd = cmd;
    infra_msg.head.len = sizeof(airvesen_ir_struct);
    infra_msg.value = value;
    ipc_send_message(sock, sizeof(airvesen_ir_struct), (char *)&infra_msg);
}

void airvisen_stop_infra_ipc()
{
    ipc_close(sock);
}


static int dispatch_track_message(int data_len, char *data)
{
    if (data_len < sizeof(airvisen_infra_ctrl_s))
    {
        return -1;
    }

    airvisen_infra_ctrl_s *msg = (airvisen_infra_ctrl_s *)data;

    printf("recvv cmmmmmmd : %d len: %d data_len: %d\n", msg->head.cmd, msg->head.len, data_len);
    return msg->head.cmd;
}

int airvisen_recv_infra_message(char *data, int data_len)
{
    int rc = ipc_recv_message(sock, data, data_len, 1);
    if (rc > 0)
    {
        return dispatch_track_message(rc, data);
    }
    else
    {
        return rc;
    }
}


int airvisen_recv_infra_message_wait(char *data, int data_len)
{
    int rc = ipc_recv_message_wait(sock, data, data_len);
    if (rc > 0)
    {
        return dispatch_track_message(rc, data);
    }
    else
    {
        return rc;
    }
}
