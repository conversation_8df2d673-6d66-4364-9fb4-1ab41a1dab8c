#include "ipc.h"
#include "video_record_message.h"


static int sock;
#define VIDEO_RECORD_SOCKET_ADDRESS "ipc:///tmp/airvisen.videorecord.ipc"

void airvisen_start_video_record_ipc()
{
    sock = ipc_server_start(VIDEO_RECORD_SOCKET_ADDRESS); 
    if (sock < 0)
    {
        printf("start ipc server faild\n");
    }  
}


void airvisen_start_video_record_client_ipc()
{
    sock = ipc_client_start(VIDEO_RECORD_SOCKET_ADDRESS); 
    if (sock < 0)
    {
        printf("start ipc server faild\n");
    }  
    
}

void airvisen_send_start_video_record_command()
{
    airvisen_track_message_store_record_s video_record_msg = {0};
    video_record_msg.head.cmd = START_VIDEO_RECORD;
    video_record_msg.head.len = sizeof(airvisen_track_message_store_record_s);
    ipc_send_message(sock, sizeof(airvisen_track_message_store_record_s), (char *)&video_record_msg);
}


void airvisen_send_stop_video_record_command()
{
    airvisen_track_message_store_record_s video_record_msg = {0};
    video_record_msg.head.cmd = STOP_VIDEO_RECORD;
    video_record_msg.head.len = sizeof(airvisen_track_message_store_record_s);
    ipc_send_message(sock, sizeof(airvisen_track_message_store_record_s), (char *)&video_record_msg);
}


void airvisen_notify_video_record_stop_event(int stop_reason)
{
    airvisen_track_message_record_stop_event_s video_record_msg = {0};
    video_record_msg.head.cmd = VIDEO_RECORD_STOP_EVENT;
    video_record_msg.head.len = sizeof(airvisen_track_message_record_stop_event_s);
    video_record_msg.stop_reason = stop_reason;//stop_reason 0 indicate auto stop after 30 mintues , 1 indicate other reason.
    ipc_send_message(sock, sizeof(airvisen_track_message_record_stop_event_s), (char *)&video_record_msg);
}

void airvisen_stop_video_record_ipc()
{
    ipc_close(sock);
}


static int dispatch_track_message(int data_len, char *data)
{
    if (data_len < sizeof(airvisen_track_message_head_s)){
        return -1;
    }
    
    airvisen_track_message_head_s *msg = (airvisen_track_message_head_s*)data;
    switch (msg->cmd)
    {
        case START_VIDEO_RECORD:{
            printf("Recv START_VIDEO_RECORD command\n");
            return START_VIDEO_RECORD;
        }
        break;

        case STOP_VIDEO_RECORD:{
            printf("Recv STOP_VIDEO_RECORD command\n");
            return STOP_VIDEO_RECORD;
        }
        break;

        case VIDEO_RECORD_STOP_EVENT:{
            printf("Recv VIDEO_RECORD_STOP_EVENT event\n");
            return VIDEO_RECORD_STOP_EVENT;
        }
        break;

    default:
        printf("unsupport command: [%d]\n",msg->cmd);
        return -1;
        break;
    }
}

/**
 * @param char *data: recv buf.
 * @param int data_len: recv buf length.
 * @param int flags: 0 indicate wait, 1 NNDONOTWAIT.
 */
int airvisen_recv_video_record_message(char *data, int data_len, int flags)
{
    int rc = ipc_recv_message(sock, data, data_len, flags);
    if (rc > 0)
    {
        return dispatch_track_message(rc, data);
    }else{
        return rc;
    }
    
}

