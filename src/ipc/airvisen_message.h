#ifndef AIRVISEN_MESSAGE_H_
#define AIRVISEN_MESSAGE_H_

typedef enum TrackingStates
{
    NORMAL_STATE = 0,
    DETECT_STATE = 1,
    START_DETECT_STATE, // 添加检测开启和关闭状态，发送指令给算法，对算法做优化
    STOP_DETECT_STATE,
    // TRACK_STATE = 2
    START_TRACK_STATE,
    STOP_TRACK_STATE,
    TRACKING_STATE,
} tracking_state_e;

// #include "sample_common.h"
typedef enum airvisen_track_cmd
{
    COMMAND_ACK = 0,

    START_JPEG_STORE = 1,

    STOP_JPEG_STORE,

    START_VIDEO_RECORD,

    STOP_VIDEO_RECORD,

    VIDEO_ZOOM,

    // Auto stop after 30 min or unexpected halt event. notify tracker main process.
    VIDEO_RECORD_STOP_EVENT,

    VIS_DETECTION_RESULT, // 7
    IR_DETECTION_RESULT,  // 8

    VIS_TRACKING_RESULT, // 9
    IR_TRACKING_RESULT,  // 10

    VIS_TRACKER_INIT_BOX,
    IR_TRACKER_INIT_BOX,

    SET_DXDY_FROM_WEB,

    TRACKER_STOP,

    TRACKER_CHANGE_CAMERA,

    UPDATE_INPUT_SENSOR_TYPE, // 更新大图sensor_type给算法
    UPDATE_TRACK_STATUS,      // 更新跟踪状态给算法

} airvisen_track_cmd_e;

/**
 * message head
 */
typedef struct airvisen_track_message_head
{
    int cmd;
    int len;
} airvisen_track_message_head_s;

/**
 * store jpeg command
 */
typedef struct airvisen_track_message_store_jpeg
{
    airvisen_track_message_head_s head;

    /**reserved */
    int reserved;
} airvisen_track_message_store_jpeg_s;

/**
 * video record command
 */
typedef struct airvisen_track_message_store_record
{
    airvisen_track_message_head_s head;

    /*single record event, save frame_count frame*/
    int frame_count;

    /*record type
    0 default, indicate record as mp4
    */
    int record_type;
} airvisen_track_message_store_record_s;

/**
 * video record auto stop or unexpected halt
 */
typedef struct airvisen_track_message_record_stop_event
{
    airvisen_track_message_head_s head;

    /*stop_reason
    0 auto stop after 30 mintues.
    1 other.
    */
    int stop_reason;
} airvisen_track_message_record_stop_event_s;

/**
 * video zoom command
 */
typedef struct airvisen_track_message_video_zoom
{
    airvisen_track_message_head_s head;

    /*zoom factor ,range from 1.0-8.0*/
    int factor;
} airvisen_track_message_video_zoom_s;

#endif /*AIRVISEN_MESSAGE_H_*/
