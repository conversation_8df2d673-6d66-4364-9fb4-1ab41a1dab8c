#ifndef REALPLAY_H
#define REALPLAY_H

#ifdef __cplusplus
extern "C"{
#endif

#include <stdlib.h>
#include <stdio.h>
#if defined(WIN32) || defined(WIN64)
#include <windows.h>
#else
#include <pthread.h>
#include <sys/time.h>
#include <signal.h>
#include <unistd.h>
#endif
#include <stdint.h>

#include <stdbool.h>

int upgrade_dahua_firmware(const char *update_file_path);
int get_dahua_firmware_version();

int start_ir_control_proc(pthread_t *tid) ;
int stop_ir_control_proc();
int cleanup_dahua();
#ifdef __cplusplus
}
#endif

#endif

