#include "realplay.h"
//If the interface returns a failure, the error code can be obtained through the USBSDK_GetLastError interface.
//#include "USBLib.h"
#include <stdio.h>
#include <pthread.h>
#include <unistd.h>
#include "infra_message.h"
#include "dictionary.h"
#include "iniparser.h"
#include "confighelper.h"
#include "host_uvc.h"



static int g_exit_flag = 0;
static pthread_t tmp_tid;
bool bReceiveData = false;

#define INFRA_SECTION "infra"
#define CFG_FILE_NAME "/app/app/configtracker.ini"
#define BRIGHTNESS_KEY "infra:brightness"
#define CONTRAST_KEY "infra:contrast"
#define GAIN_KEY "infra:gain"
#define PSEUDO_KEY "infra:pseudo_color"
#define LCE_KEY "infra:lce"
#define ALAMR_SWITCH "infra:alarm_switch"
#define ALARM_TEMPERATURE "infra:alarm_temperature"


#define DEFAULT_BRIGHTNESS 50
#define DEFAULT_CONTRAST   50
#define DEFAULT_GAIN 		1
#define DEFAULT_COLOR 		0
#define DEFAULT_LCE 		64

static int brightness;
static int contrast;
static int gain;
static int pseudo_color;
static int alarm_switch;
static int alarm_temperature;
static int lce;
// static float zoom_factor = 1.0;
float zoom_factor = 1.0;

zoom_pair g_zoom_pair = {1.0f, 1.0f, 1.0f};

zoom_pair choose_zoom(float target_zoom)
{
    /* 允许的 SDK 倍率，必须升序 */
    const float sdk_tbl[6] 		= {1.0f, 2.0f, 3.2f, 4.0f, 6.4f, 8.0f};
    const float real_sdk_tbl[6] = {1.1f, 2.7f, 3.3f, 3.5f, 3.8f, 3.9f};
    const int n = sizeof(sdk_tbl) / sizeof(sdk_tbl[0]);

    /* 截断到合法区间 */
    if (target_zoom <= 1.0f) target_zoom = 1.0f;
    if (target_zoom >= 8.0f) target_zoom = 8.0f;

    /* 向下取最大的可用 SDK 倍率 */
    float sdk_zoom = 1.0f;
    float real_sdk_zoom = 1.0f;
    for (int i = 0; i < n; ++i)
    {
        if (sdk_tbl[i] <= target_zoom)
		{
            sdk_zoom = sdk_tbl[i];
			real_sdk_zoom = real_sdk_tbl[i];
		}
        else
            break;
    }

    zoom_pair res;
    res.sdk_zoom = sdk_zoom;
	res.real_sdk_zoom = real_sdk_zoom;
    res.custom_zoom = target_zoom / sdk_zoom;
    return res;
}

int load_cfg_from_ini(const char *cfg_file_path)
{
    dictionary  *   ini ;

    /* Some temporary variables to hold query results */
    // int             b ;
    // int             i ;
    // double          d ;
    // const char  *   s ;

    ini = iniparser_load(cfg_file_path);
    if (ini==NULL) {
        fprintf(stderr, "cannot parse file: %s\n", cfg_file_path);
        return -1 ;
    }

    brightness = iniparser_getint(ini, BRIGHTNESS_KEY, DEFAULT_BRIGHTNESS);
    printf("brightness:       [%d]\n", brightness);

	contrast = iniparser_getint(ini, CONTRAST_KEY, DEFAULT_CONTRAST);
    printf("contrast:       [%d]\n", contrast);

	gain = iniparser_getint(ini, GAIN_KEY, DEFAULT_GAIN);
    printf("gain:       [%d]\n", gain);

	pseudo_color = iniparser_getint(ini, PSEUDO_KEY, DEFAULT_COLOR);
    printf("pseudo_color:       [%d]\n", pseudo_color);

	lce = iniparser_getint(ini, LCE_KEY, DEFAULT_LCE);
	printf("lce:       [%d]\n", lce);


	alarm_switch = iniparser_getint(ini, ALAMR_SWITCH, 0);
    printf("alarm_switch:       [%d]\n", alarm_switch);

	alarm_temperature = iniparser_getint(ini, ALARM_TEMPERATURE, 100);
    printf("alarm_temperature:       [%d]\n", alarm_temperature);

	

	if (USBSDK_SetBrightness(brightness))
	{
		printf("set brightness : %d\n", brightness);
	}

	if (USBSDK_SetContrast(contrast))
	{
		printf("set Contrast : %d\n", contrast);
	}

	if (USBSDK_SetGainAuto(gain))
	{
		printf("set GainAuto : %d\n", gain);
	}

	if (USBSDK_SetColor(pseudo_color))
	{
		printf("set color : %d\n", pseudo_color);
	}

    iniparser_freedict(ini);
    return 0 ;
}


void get_default_value()
{
	int value;
	printf("\n\n\n\n");
	if (USBSDK_GetBrightness(&value))
	{
		printf("brightness : %d\n", value);
	}

	if (USBSDK_GetContrast(&value))
	{
		printf("Contrast : %d\n", value);
	}

	if (USBSDK_GetGainAuto(&value))
	{
		printf("GainAuto : %d\n", value);
	}


	if (USBSDK_GetLCEValue(&value))
	{
		printf("LCE : %d\n", value);
	}

	if (USBSDK_GetColor(&value)){
		printf("color : %d\n", value);
	}
}

#define MEASURE_RECT_WIDTH 	8191
#define MEASURE_RECT_HEIGHT 8191

#define MEASURE_CENTER_RECT_WIDTH  2
#define MEASURE_CENTER_RECT_HEIGHT 2


static int exit_get_upgrade_progress = 0;

void *get_upgrade_progress(void *param)
{
	printf("get_upgrade_progress\n");
	int maxlen;
	int offset;
	
	while(!exit_get_upgrade_progress)
	{
		
		if(!USBSDK_GetProgress(&maxlen, &offset))
		{
			printf("USBSDK_GetProgress Failed, ErrCode: %d\n", USBSDK_GetLastError());
		}
		else
		{
			printf("maxlen: %d, offset: %d\n", maxlen, offset);

		}
		sleep(1);

	}
	
}


int start_get_upgrade_progress_proc() 
{
    // frame consumer
	pthread_t progress_id;
    if (pthread_create(&progress_id, NULL, get_upgrade_progress, NULL) != 0) {
        perror("pthread_create");
        return EXIT_FAILURE;
    }

	printf("start_get_upgrade_progress_proc succ\n ");
	
}


void *ir_control_thread(void *arg)
{

	printf("--------ir_control_thread start----------\n");
	unsigned int count = 0;
    sleep(1);
    while(!g_exit_flag)
    {
		count ++;
		//set color 
		char data[256] = {0};
		int command = airvisen_recv_infra_message(data, sizeof(data));
		if (command == INFRA_CORRECT_SHUTTER)
		{
			// if (!USBSDK_ShutDown())
			// {
			// 	printf("USBSDK_ShutDown Failed, ErrCode: %d\n", USBSDK_GetLastError());
			// }
			// else{
			// 	printf("USBSDK_ShutDown ok\n");
			// }
			continue;
		}
		else if (command == INFRA_ADD_CONTRAST)
		{
			if (contrast + 1 > 100)
			{
				continue;
			}
			
			contrast = contrast + 1;
			char tmp[32] = {0};
			sprintf(tmp, "%d", contrast);
			if(write_to_ini(CFG_FILE_NAME, INFRA_SECTION, CONTRAST_KEY, tmp) != 0){
				printf("write to contrast cfg failed\n");
			}

			// if (!USBSDK_SetContrast(contrast))
			// {
			// 	printf("USBSDK_SetContrast Failed, ErrCode: %d\n", USBSDK_GetLastError());
			// }else{
			// 	printf("USBSDK_SetContrast ok\n");
			// }
			continue;
		}
		else if (command == INFRA_SUB_CONTRAST)
		{
			if (contrast - 1 < 0)
			{
				continue;
			}
			
			contrast = contrast - 1;
			char tmp[32] = {0};
			sprintf(tmp, "%d", contrast);
			write_to_ini(CFG_FILE_NAME, INFRA_SECTION, CONTRAST_KEY, tmp);
			// if (!USBSDK_SetContrast(contrast))
			// {
			// 	printf("USBSDK_SetContrast Failed, ErrCode: %d\n", USBSDK_GetLastError());
			// }
			// else{
			// 	printf("USBSDK_SetContrast ok\n");
			// }
			continue;
		}
		else if (command == INFRA_ADD_BRIGHTNESS)
		{
			if (brightness + 1 > 100)
			{
				continue;
			}
			
			brightness = brightness + 1;
			char tmp[32] = {0};
			sprintf(tmp, "%d", brightness);
			if(write_to_ini(CFG_FILE_NAME, INFRA_SECTION, BRIGHTNESS_KEY, tmp) != 0){
				printf("write to brightness cfg failed\n");
			}
			// if (!USBSDK_SetBrightness(brightness))
			// {
			// 	printf("USBSDK_SetBrightness Failed, ErrCode: %d\n", USBSDK_GetLastError());
			// }else{
			// 	printf("USBSDK_SetBrightness ok\n");
			// }
			continue;
		}
		else if (command == INFRA_SUB_BRIGHTNESS)
		{
			if (brightness - 1 < 0)
			{
				continue;
			}
			
			brightness = brightness - 1;
			char tmp[32] = {0};
			sprintf(tmp, "%d", brightness);
			write_to_ini(CFG_FILE_NAME,  INFRA_SECTION, BRIGHTNESS_KEY, tmp);
			// if (!USBSDK_SetBrightness(brightness))
			// {
			// 	printf("USBSDK_SetBrightness Failed, ErrCode: %d\n", USBSDK_GetLastError());
			// }
			continue;
		}
		else if (command == INFRA_RESET_BRI_CONTRAST_DEFAULT)
		{
			brightness = DEFAULT_BRIGHTNESS;
			
			char tmp[32] = {0};
			sprintf(tmp, "%d", brightness);
			write_to_ini(CFG_FILE_NAME, INFRA_SECTION, BRIGHTNESS_KEY, tmp);
			// if (!USBSDK_SetBrightness(brightness))
			// {
			// 	printf("USBSDK_SetBrightness Failed, ErrCode: %d\n", USBSDK_GetLastError());
			// 	continue;
			// }else{
			// 	printf("USBSDK_SetBrightness ok\n");
			// }
			contrast = DEFAULT_CONTRAST;
			memset(tmp,0,32);
			sprintf(tmp, "%d", contrast);
			write_to_ini(CFG_FILE_NAME, INFRA_SECTION,  CONTRAST_KEY, tmp);
			// if (!USBSDK_SetContrast(contrast))
			// {
			// 	printf("USBSDK_SetContrast Failed, ErrCode: %d\n", USBSDK_GetLastError());
			// }else{
			// 	printf("USBSDK_SetContrast ok\n");
			// }
			continue;
		}
		else if (command == INFRA_SET_GAINAUTO)
		{
			airvisen_set_gainauto * p = (airvisen_set_gainauto *)data;
			gain = p->value;
			char tmp[32] = {0};
			sprintf(tmp, "%d", gain);
			printf("\n\n gain: %d\n", gain);
			write_to_ini(CFG_FILE_NAME, INFRA_SECTION,  GAIN_KEY, tmp);
			// if (!USBSDK_SetLCEValue(gain*18))
			// {
			// 	printf("USBSDK_SetLCEValue Failed, ErrCode: %d\n", USBSDK_GetLastError());
			// }
			// else{
			// 	printf("USBSDK_SetLCEValue ok\n");
			// }
			continue;
		}
		else if (command == INFRA_SET_COLOR)
		{
			//大华机芯
			// 			0-白热，1-黑热，2-聚变，3-彩虹，4-金秋，5-午日，
			// 6-铁红，7-琥珀，8-玉石，9-夕阳，10-冰火，11-油画，
			// 12-石榴，13-翡翠，14-春，15-夏，16-秋，17-冬，18热检测，19-极光

			//天进
			// 0x00：白热（默认）
			// 0x01：黑热
			// 0x02：彩虹
			// 0x03：高对比度彩虹
			// 0x04：铁红
			// 0x05：熔岩
			// 0x06：天空
			// 0x07：中灰
			// 0x08：灰红
			// 0x09：紫橙
			// 0x0B：警示红
			// 0x0C：冰火
			airvisen_infra_set_color_s * p = (airvisen_infra_set_color_s *)data;
			printf("pseudo_color: %d \n", p->color);
			pseudo_color =  p->color;
			int real_color = pseudo_color;
			char tmp[32] = {0};
			sprintf(tmp, "%d", pseudo_color);
			write_to_ini(CFG_FILE_NAME,  INFRA_SECTION, PSEUDO_KEY, tmp);

			// if (!USBSDK_SetColor(pseudo_color))
			// {
			// 	printf("USBSDK_SetColor Failed, ErrCode: %d\n", USBSDK_GetLastError());
			// }
			// else{
			// 	printf("USBSDK_SetColor ok\n");
			// }
			continue;
		}
		else if (command == INFRA_ZOOM_STOP)
		{
			zoom_factor = 1.0;
			printf("\n\n digital zoom: %f\n", zoom_factor);
			//write_to_ini(CFG_FILE_NAME, INFRA_SECTION, "DIGITAL_ZOOM_KEY", tmp);
			continue;
		}
		else if (command == INFRA_ZOOM_PLUS || command == INFRA_ZOOM_BIG_PLUS)
		{

			if(zoom_factor < 8.0)
			{
				zoom_factor = zoom_factor + 0.1;
			}

			
			printf("\n\n digital zoom: %d\n", zoom_factor);
			continue;
		}
		else if (command == INFRA_ZOOM_MINUS || command == INFRA_ZOOM_BIG_MINUS)
		{
			if (zoom_factor > 1.0)
			{
				zoom_factor = zoom_factor - 0.1;
			}
			printf("\n\n digital zoom: %f\n", zoom_factor);
			continue;
		}
		else if (command == INFRA_ZOOM_SET)
		{
			
			airvisen_set_digtal_zoom * p = (airvisen_set_digtal_zoom *)data;
			zoom_factor = p->value/10.0;
			printf("\n\n digital zoom: %f\n", zoom_factor);
			continue;
		
		}

		//sleep 50ms
        usleep(50000);
    }
}

int start_ir_control_proc(pthread_t *tid) 
{
    // frame consumer
    if (pthread_create(tid, NULL, ir_control_thread, NULL) != 0) {
        perror("pthread_create");
        return EXIT_FAILURE;
    }

	
}



int stop_ir_control_proc()
{
	g_exit_flag = 1;
}





